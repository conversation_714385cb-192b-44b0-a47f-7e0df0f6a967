# Excel文件整合和统计工具使用说明

## 功能概述

这个Python程序可以：
1. 递归读取指定文件夹及其子文件夹中的所有Excel文件（.xlsx, .xls, .xlsm）
2. 按文件名称分类整合数据
3. 生成详细的统计报告
4. 将所有数据和统计信息保存到一个新的Excel文件中

## 安装依赖

在运行程序前，请确保安装了以下Python包：

```bash
pip install pandas openpyxl xlrd
```

## 使用方法

1. **运行程序**：
   ```bash
   python ceshi2.py
   ```

2. **输入文件夹路径**：
   - 程序会提示您输入要处理的文件夹路径
   - 可以是绝对路径或相对路径
   - 例如：`C:\Users\<USER>\Documents\Excel文件夹` 或 `./数据文件夹`

3. **等待处理**：
   - 程序会自动查找所有Excel文件
   - 显示处理进度和统计信息

4. **保存结果**：
   - 程序会询问输出文件名
   - 直接回车使用默认名称 `整合结果.xlsx`
   - 或输入自定义文件名

## 输出文件结构

生成的Excel文件包含以下工作表：

### 1. 统计概览
- 总文件数
- 总数据行数
- 总文件大小
- 平均文件大小
- 平均数据行数
- 处理时间
- 分类统计概要

### 2. 文件信息
详细的文件信息列表：
- 文件名
- 文件路径
- 工作表数量
- 文件大小(KB)
- 修改时间
- 数据行数
- 数据列数

### 3. 分类统计
按文件名分类的统计信息：
- 文件数量
- 总行数
- 总列数

### 4. 数据工作表
按文件名分类的实际数据，每个分类一个工作表：
- 包含原始数据的所有列
- 添加了"来源工作表"和"来源文件"列用于追溯数据来源

## 特性

- **递归搜索**：自动搜索子文件夹中的Excel文件
- **多工作表支持**：读取每个Excel文件的所有工作表
- **数据追溯**：为每行数据添加来源信息
- **错误处理**：跳过无法读取的文件并显示警告
- **统计分析**：提供详细的数据统计信息
- **分类整合**：按文件名自动分类整合数据

## 示例

假设您有以下文件结构：
```
数据文件夹/
├── 销售数据/
│   ├── 销售报表_1月.xlsx
│   └── 销售报表_2月.xlsx
├── 库存数据/
│   └── 库存报表_1月.xlsx
└── 汇总报表.xlsx
```

程序会生成包含以下工作表的结果文件：
- 统计概览
- 文件信息
- 分类统计
- 销售报表_1月
- 销售报表_2月
- 库存报表_1月
- 汇总报表

## 注意事项

1. **文件格式**：支持 .xlsx、.xls、.xlsm 格式
2. **工作表名称**：Excel工作表名称限制为31个字符
3. **内存使用**：处理大量数据时可能需要较多内存
4. **文件权限**：确保对目标文件夹有读取权限
5. **输出位置**：结果文件保存在程序运行目录

## 错误处理

程序会自动处理以下情况：
- 文件夹不存在
- 无法读取的Excel文件
- 空的工作表
- 文件权限问题

遇到错误时会显示警告信息但不会中断整个处理过程。

## 测试

可以运行 `create_test_data.py` 创建测试数据，然后使用 `check_results.py` 验证结果。
