import pandas as pd
import os
from pathlib import Path

def create_test_excel_files():
    """创建一些测试用的Excel文件"""
    
    # 创建测试文件夹
    test_folder = Path("test_excel_data")
    test_folder.mkdir(exist_ok=True)
    
    # 创建子文件夹
    subfolder1 = test_folder / "销售数据"
    subfolder2 = test_folder / "库存数据"
    subfolder1.mkdir(exist_ok=True)
    subfolder2.mkdir(exist_ok=True)
    
    # 创建测试数据1 - 销售数据
    sales_data1 = {
        '产品名称': ['苹果', '香蕉', '橙子', '葡萄', '草莓'],
        '销售数量': [100, 150, 80, 120, 90],
        '单价': [5.5, 3.2, 4.8, 8.0, 12.5],
        '销售额': [550, 480, 384, 960, 1125],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    }
    
    sales_data2 = {
        '产品名称': ['苹果', '香蕉', '橙子', '西瓜', '桃子'],
        '销售数量': [80, 200, 60, 50, 70],
        '单价': [5.5, 3.2, 4.8, 6.0, 7.5],
        '销售额': [440, 640, 288, 300, 525],
        '日期': ['2024-01-06', '2024-01-07', '2024-01-08', '2024-01-09', '2024-01-10']
    }
    
    # 创建库存数据
    inventory_data1 = {
        '产品名称': ['苹果', '香蕉', '橙子', '葡萄', '草莓'],
        '库存数量': [500, 300, 200, 150, 100],
        '进货价': [4.0, 2.5, 3.5, 6.0, 10.0],
        '库存价值': [2000, 750, 700, 900, 1000],
        '供应商': ['供应商A', '供应商B', '供应商A', '供应商C', '供应商B']
    }
    
    inventory_data2 = {
        '产品名称': ['西瓜', '桃子', '梨', '柚子', '芒果'],
        '库存数量': [80, 120, 200, 60, 40],
        '进货价': [4.5, 6.0, 3.0, 8.0, 15.0],
        '库存价值': [360, 720, 600, 480, 600],
        '供应商': ['供应商D', '供应商A', '供应商C', '供应商B', '供应商D']
    }
    
    # 保存销售数据文件
    with pd.ExcelWriter(subfolder1 / "销售报表_1月.xlsx", engine='openpyxl') as writer:
        pd.DataFrame(sales_data1).to_excel(writer, sheet_name='第一周', index=False)
        pd.DataFrame(sales_data2).to_excel(writer, sheet_name='第二周', index=False)
    
    # 保存另一个销售数据文件
    pd.DataFrame(sales_data1).to_excel(subfolder1 / "销售报表_2月.xlsx", index=False)
    
    # 保存库存数据文件
    with pd.ExcelWriter(subfolder2 / "库存报表_1月.xlsx", engine='openpyxl') as writer:
        pd.DataFrame(inventory_data1).to_excel(writer, sheet_name='水果库存', index=False)
        pd.DataFrame(inventory_data2).to_excel(writer, sheet_name='新品库存', index=False)
    
    # 在主文件夹创建一个汇总文件
    summary_data = {
        '类别': ['水果', '蔬菜', '肉类', '饮料'],
        '总销售额': [5000, 3000, 8000, 2000],
        '总库存值': [15000, 8000, 12000, 5000],
        '利润率': [0.25, 0.30, 0.20, 0.35]
    }
    pd.DataFrame(summary_data).to_excel(test_folder / "汇总报表.xlsx", index=False)
    
    print(f"测试数据已创建在文件夹: {test_folder.absolute()}")
    print("包含以下文件:")
    for file_path in test_folder.rglob("*.xlsx"):
        print(f"  - {file_path.relative_to(test_folder)}")

if __name__ == "__main__":
    create_test_excel_files()
