import os
import pandas as pd
from pathlib import Path
import warnings
from datetime import datetime
from collections import defaultdict

# 忽略警告信息
warnings.filterwarnings('ignore')

class ExcelProcessor:
    def __init__(self):
        self.all_data = defaultdict(list)  # 按文件名分类存储数据
        self.file_info = []  # 存储文件信息
        self.statistics = {}  # 存储统计信息

    def find_excel_files(self, folder_path):
        """递归查找文件夹中的所有Excel文件"""
        excel_files = []
        folder_path = Path(folder_path)

        if not folder_path.exists():
            print(f"错误：文件夹 '{folder_path}' 不存在！")
            return excel_files

        # 支持的Excel文件扩展名
        excel_extensions = ['.xlsx', '.xls', '.xlsm']

        for root, _, files in os.walk(folder_path):
            for file in files:
                if any(file.lower().endswith(ext) for ext in excel_extensions):
                    file_path = Path(root) / file
                    excel_files.append(file_path)

        return excel_files

    def read_excel_file(self, file_path):
        """读取单个Excel文件的所有工作表"""
        try:
            # 获取所有工作表名称
            excel_file = pd.ExcelFile(file_path)
            sheets_data = {}

            for sheet_name in excel_file.sheet_names:
                try:
                    # 读取工作表数据
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    if not df.empty:
                        sheets_data[sheet_name] = df
                except Exception as e:
                    print(f"警告：无法读取工作表 '{sheet_name}' 在文件 '{file_path}': {e}")

            return sheets_data

        except Exception as e:
            print(f"错误：无法读取文件 '{file_path}': {e}")
            return {}

    def process_files(self, folder_path):
        """处理文件夹中的所有Excel文件"""
        print(f"开始处理文件夹: {folder_path}")

        # 查找所有Excel文件
        excel_files = self.find_excel_files(folder_path)

        if not excel_files:
            print("未找到任何Excel文件！")
            return

        print(f"找到 {len(excel_files)} 个Excel文件")

        # 处理每个文件
        for i, file_path in enumerate(excel_files, 1):
            print(f"正在处理 ({i}/{len(excel_files)}): {file_path.name}")

            # 读取文件数据
            sheets_data = self.read_excel_file(file_path)

            if sheets_data:
                # 记录文件信息
                file_info = {
                    '文件名': file_path.name,
                    '文件路径': str(file_path),
                    '工作表数量': len(sheets_data),
                    '文件大小(KB)': round(file_path.stat().st_size / 1024, 2),
                    '修改时间': datetime.fromtimestamp(file_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                }

                # 合并所有工作表的数据
                combined_df = pd.DataFrame()
                for sheet_name, df in sheets_data.items():
                    # 添加来源信息
                    df_copy = df.copy()
                    df_copy['来源工作表'] = sheet_name
                    df_copy['来源文件'] = file_path.name
                    combined_df = pd.concat([combined_df, df_copy], ignore_index=True)

                if not combined_df.empty:
                    # 按文件名分类存储数据
                    file_key = file_path.stem  # 不包含扩展名的文件名
                    self.all_data[file_key].append(combined_df)

                    file_info['数据行数'] = len(combined_df)
                    file_info['数据列数'] = len(combined_df.columns)
                else:
                    file_info['数据行数'] = 0
                    file_info['数据列数'] = 0

                self.file_info.append(file_info)

    def generate_statistics(self):
        """生成数据统计"""
        print("正在生成统计信息...")

        total_files = len(self.file_info)
        total_rows = sum(info['数据行数'] for info in self.file_info)
        total_size = sum(info['文件大小(KB)'] for info in self.file_info)

        self.statistics = {
            '总文件数': total_files,
            '总数据行数': total_rows,
            '总文件大小(KB)': round(total_size, 2),
            '平均文件大小(KB)': round(total_size / total_files if total_files > 0 else 0, 2),
            '平均数据行数': round(total_rows / total_files if total_files > 0 else 0, 2),
            '处理时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 按文件名分类的统计
        category_stats = {}
        for category, data_list in self.all_data.items():
            combined_df = pd.concat(data_list, ignore_index=True) if data_list else pd.DataFrame()
            category_stats[category] = {
                '文件数量': len(data_list),
                '总行数': len(combined_df),
                '总列数': len(combined_df.columns) if not combined_df.empty else 0
            }

        self.statistics['分类统计'] = category_stats

    def save_results(self, output_path='整合结果.xlsx'):
        """保存整合结果到Excel文件"""
        print(f"正在保存结果到: {output_path}")

        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 1. 保存统计概览
            stats_df = pd.DataFrame([self.statistics])
            stats_df.to_excel(writer, sheet_name='统计概览', index=False)

            # 2. 保存文件信息
            if self.file_info:
                files_df = pd.DataFrame(self.file_info)
                files_df.to_excel(writer, sheet_name='文件信息', index=False)

            # 3. 保存分类统计
            if self.statistics.get('分类统计'):
                category_stats_df = pd.DataFrame(self.statistics['分类统计']).T
                category_stats_df.to_excel(writer, sheet_name='分类统计')

            # 4. 按分类保存数据
            for category, data_list in self.all_data.items():
                if data_list:
                    # 合并同一分类的所有数据
                    combined_df = pd.concat(data_list, ignore_index=True)

                    # 限制工作表名称长度（Excel限制31个字符）
                    sheet_name = category[:31] if len(category) > 31 else category

                    # 确保工作表名称唯一
                    original_name = sheet_name
                    counter = 1
                    while sheet_name in writer.sheets:
                        sheet_name = f"{original_name[:28]}_{counter}"
                        counter += 1

                    combined_df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"结果已保存到: {output_path}")

def main():
    """主函数"""
    print("=" * 50)
    print("Excel文件整合和统计工具")
    print("=" * 50)

    # 获取用户输入的文件夹路径
    while True:
        folder_path = input("请输入要处理的文件夹路径: ").strip()
        if folder_path:
            break
        print("请输入有效的文件夹路径！")

    # 创建处理器实例
    processor = ExcelProcessor()

    try:
        # 处理文件
        processor.process_files(folder_path)

        if not processor.all_data:
            print("没有找到可处理的数据！")
            return

        # 生成统计
        processor.generate_statistics()

        # 显示统计信息
        print("\n" + "=" * 30)
        print("处理完成！统计信息：")
        print("=" * 30)
        for key, value in processor.statistics.items():
            if key != '分类统计':
                print(f"{key}: {value}")

        print("\n分类统计：")
        for category, stats in processor.statistics.get('分类统计', {}).items():
            print(f"  {category}: {stats}")

        # 保存结果
        output_path = input("\n请输入输出文件名 (直接回车使用默认名称'整合结果.xlsx'): ").strip()
        if not output_path:
            output_path = '整合结果.xlsx'

        processor.save_results(output_path)

        print(f"\n处理完成！结果已保存到: {output_path}")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()