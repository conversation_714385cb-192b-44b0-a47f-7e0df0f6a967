import pandas as pd

def check_results():
    """检查整合结果文件的内容"""
    
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile('整合结果.xlsx')
        
        print("=" * 50)
        print("整合结果文件内容检查")
        print("=" * 50)
        
        print(f"工作表列表: {excel_file.sheet_names}")
        print()
        
        # 检查每个工作表
        for sheet_name in excel_file.sheet_names:
            print(f"工作表: {sheet_name}")
            print("-" * 30)
            
            df = pd.read_excel('整合结果.xlsx', sheet_name=sheet_name)
            print(f"数据形状: {df.shape}")
            print("前几行数据:")
            print(df.head())
            print()
            
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

if __name__ == "__main__":
    check_results()
